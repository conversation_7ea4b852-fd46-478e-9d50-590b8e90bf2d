package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.76

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	// Extract user ID from JWT token instead of accepting it as input
	userId := GetUserIDFromContext(ctx)
	if userId == uuid.Nil {
		return nil, fmt.Errorf("User ID not found in JWT token")
	}

	referrer, err := r.UserService.GetUserByInvitationCode(ctx, input.InvitationCode)
	if err != nil || referrer == nil {
		return nil, fmt.Errorf("Invalid invitation code")
	}

	err = r.UserService.CreateUserWithReferral(ctx, referrer.ID, userId.String())
	if err != nil {
		return nil, fmt.Errorf("Failed to create user association: %v", err)
	}

	// Fetch the user to return in the response (required by schema)
	user, err := r.UserService.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("Failed to fetch user after creating referral: %v", err)
	}

	return &gql_model.CreateUserResponse{
		User:    ModelUserToGQL(user),
		Success: true,
		Message: "User created and bound referral relationship successfully",
	}, nil
}

// UpdateFirstLoginStatus is the resolver for the updateFirstLoginStatus field.
func (r *mutationResolver) UpdateFirstLoginStatus(ctx context.Context) (*gql_model.User, error) {
	userId := GetUserIDFromContext(ctx)
	user, err := r.UserService.UpdateFirstLoginStatus(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to update first login status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UpdateWalletExportStatus is the resolver for the updateWalletExportStatus field.
func (r *mutationResolver) UpdateWalletExportStatus(ctx context.Context) (*gql_model.User, error) {
	userId := GetUserIDFromContext(ctx)
	user, err := r.UserService.UpdateWalletExportStatus(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to update wallet export status: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	userId := GetUserIDFromContext(ctx)
	// test
	// userId := uuid.Nil
	// if input.UserID != nil {
	// 	id, err := uuid.Parse(*input.UserID)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("Invalid user ID: %w", err)
	// 	}
	// 	userId = id
	// }

	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return &gql_model.CreateUserResponse{
				User:    nil,
				Success: false,
				Message: "Invalid wallet ID",
			}, nil
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return &gql_model.CreateUserResponse{
				User:    nil,
				Success: false,
				Message: "Invalid wallet account ID",
			}, nil
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}
	chain := ""
	if input.Chain != nil {
		chain = *input.Chain
	}
	walletAddress := ""
	if input.WalletAddress != nil {
		walletAddress = *input.WalletAddress
	}
	email := ""
	if input.Email != nil {
		email = *input.Email
	}
	isFirstLogin := false
	if input.IsFirstLogin != nil {
		isFirstLogin = *input.IsFirstLogin
	}
	isExportedWallet := false
	if input.IsExportedWallet != nil {
		isExportedWallet = *input.IsExportedWallet
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	user, err := r.UserService.UpdateUserInvitationCode(ctx, userId,
		chain, name, walletAddress, walletID, walletAccountID,
		walletType, input.InvitationCode, email, isFirstLogin, isExportedWallet)
	if err != nil {
		return nil, fmt.Errorf("failed to update user invitation code: %w", err)
	}

	return &gql_model.CreateUserResponse{
		User:    ModelUserToGQL(user),
		Success: true,
		Message: "User created successfully",
	}, nil
}

// User is the resolver for the user field.
func (r *queryResolver) User(ctx context.Context) (*gql_model.User, error) {
	userId := GetUserIDFromContext(ctx)
	user, err := r.UserService.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return ModelUserToGQL(user), nil
}

// UserWallets is the resolver for the userWallets field.
func (r *queryResolver) UserWallets(ctx context.Context) ([]*gql_model.UserWallet, error) {
	userId := GetUserIDFromContext(ctx)
	wallets, err := r.UserService.GetUserWallets(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user wallets: %w", err)
	}

	var gqlWallets []*gql_model.UserWallet
	for _, wallet := range wallets {
		gqlWallets = append(gqlWallets, ModelUserWalletToGQL(&wallet))
	}

	return gqlWallets, nil
}

// ReferralInfo is the resolver for the referralInfo field.
func (r *queryResolver) ReferralInfo(ctx context.Context) (*gql_model.Referral, error) {
	userId := GetUserIDFromContext(ctx)

	referral, err := r.UserService.GetReferralInfo(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral info: %w", err)
	}

	return ModelReferralToGQL(referral), nil
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	userId := GetUserIDFromContext(ctx)
	snapshot, err := r.UserService.GetReferralSnapshot(ctx, userId)
	if err != nil {
		return ModelReferralSnapshotToGQL(&model.ReferralSnapshot{}), nil
	}

	return ModelReferralSnapshotToGQL(snapshot), nil
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
